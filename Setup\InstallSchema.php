<?php

namespace Ma<PERSON><PERSON><PERSON><PERSON>\KluwerExport\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\DB\Ddl\Table;

class InstallSchema implements InstallSchemaInterface
{
    /**
     * {@inheritdoc}
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        // Create kluwer_export_log table
        $table = $setup->getConnection()->newTable(
            $setup->getTable('kluwer_export_log')
        )->addColumn(
            'log_id',
            Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'nullable' => false, 'primary' => true, 'unsigned' => true],
            'Log ID'
        )->addColumn(
            'period_folder',
            Table::TYPE_TEXT,
            50,
            ['nullable' => false],
            'Period Folder'
        )->addColumn(
            'sales_filename',
            Table::TYPE_TEXT,
            255,
            ['nullable' => false],
            'Sales Filename'
        )->addColumn(
            'customers_filename',
            Table::TYPE_TEXT,
            255,
            ['nullable' => false],
            'Customers Filename'
        )->addColumn(
            'from_date',
            Table::TYPE_DATE,
            null,
            ['nullable' => false],
            'From Date'
        )->addColumn(
            'to_date',
            Table::TYPE_DATE,
            null,
            ['nullable' => false],
            'To Date'
        )->addColumn(
            'record_count',
            Table::TYPE_INTEGER,
            null,
            ['nullable' => false, 'default' => 0],
            'Record Count'
        )->addColumn(
            'created_at',
            Table::TYPE_TIMESTAMP,
            null,
            ['nullable' => false, 'default' => Table::TIMESTAMP_INIT],
            'Created At'
        )->addColumn(
            'admin_user_id',
            Table::TYPE_INTEGER,
            null,
            ['nullable' => true, 'unsigned' => true],
            'Admin User ID'
        )->addIndex(
            $setup->getIdxName('kluwer_export_log', ['period_folder']),
            ['period_folder']
        )->addIndex(
            $setup->getIdxName('kluwer_export_log', ['created_at']),
            ['created_at']
        )->setComment(
            'Kluwer Export Log Table'
        );

        $setup->getConnection()->createTable($table);

        $setup->endSetup();
    }
}
