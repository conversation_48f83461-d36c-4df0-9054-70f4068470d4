<?php

namespace Mageyazilim\KluwerExport\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\UrlInterface;

class Actions extends Column
{
    /**
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $name = $this->getData('name');
                if (isset($item['log_id'])) {
                    $item[$name]['download_sales'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'kluwerexport/export/download',
                            ['file' => $item['sales_filename'], 'period' => $item['period_folder']]
                        ),
                        'label' => __('Download Sales'),
                        'target' => '_blank'
                    ];
                    $item[$name]['download_customers'] = [
                        'href' => $this->urlBuilder->getUrl(
                            'kluwerexport/export/download',
                            ['file' => $item['customers_filename'], 'period' => $item['period_folder']]
                        ),
                        'label' => __('Download Customers'),
                        'target' => '_blank'
                    ];
                }
            }
        }

        return $dataSource;
    }
}
