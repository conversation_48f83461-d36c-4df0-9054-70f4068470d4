<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Setup\Patch\Schema;

use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Framework\DB\Ddl\Table;

class CreateExportLogTable implements SchemaPatchInterface
{
    /**
     * @var SchemaSetupInterface
     */
    private $schemaSetup;

    /**
     * @param SchemaSetupInterface $schemaSetup
     */
    public function __construct(SchemaSetupInterface $schemaSetup)
    {
        $this->schemaSetup = $schemaSetup;
    }

    /**
     * Apply patch
     */
    public function apply()
    {
        $installer = $this->schemaSetup;
        $installer->startSetup();

        if (!$installer->tableExists('kluwer_export_log')) {
            $table = $installer->getConnection()->newTable(
                $installer->getTable('kluwer_export_log')
            )->addColumn(
                'export_id',
                Table::TYPE_INTEGER,
                null,
                [
                    'identity' => true,
                    'nullable' => false,
                    'primary'  => true,
                    'unsigned' => true,
                ],
                'Export ID'
            )->addColumn(
                'period',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Export Period'
            )->addColumn(
                'filename',
                Table::TYPE_TEXT,
                255,
                ['nullable' => false],
                'Export Filename'
            )->addColumn(
                'from_date',
                Table::TYPE_DATE,
                null,
                ['nullable' => true],
                'From Date'
            )->addColumn(
                'to_date',
                Table::TYPE_DATE,
                null,
                ['nullable' => true],
                'To Date'
            )->addColumn(
                'orders_count',
                Table::TYPE_INTEGER,
                null,
                ['nullable' => false, 'default' => 0],
                'Orders Count'
            )->addColumn(
                'created_at',
                Table::TYPE_TIMESTAMP,
                null,
                ['nullable' => false, 'default' => Table::TIMESTAMP_INIT],
                'Created At'
            )->setComment('Kluwer Export Log Table');

            $installer->getConnection()->createTable($table);
        }

        $installer->endSetup();
    }

    /**
     * Get dependencies
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Get aliases
     */
    public function getAliases()
    {
        return [];
    }
}
