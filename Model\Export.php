<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Helper\Data as HelperData;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Mageya<PERSON>lim\KluwerExport\Model\ExportLogFactory;

class Export extends AbstractModel
{
    /**
     * @var OrderCollectionFactory
     */
    protected $orderCollectionFactory;

    /**
     * @var CustomerCollectionFactory
     */
    protected $customerCollectionFactory;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var HelperData
     */
    protected $helperData;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var ExportLogFactory
     */
    protected $exportLogFactory;

    /**
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param CustomerCollectionFactory $customerCollectionFactory
     * @param Filesystem $filesystem
     * @param HelperData $helperData
     * @param DateTime $dateTime
     * @param ExportLogFactory $exportLogFactory
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        CustomerCollectionFactory $customerCollectionFactory,
        Filesystem $filesystem,
        HelperData $helperData,
        DateTime $dateTime,
        ExportLogFactory $exportLogFactory
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->filesystem = $filesystem;
        $this->helperData = $helperData;
        $this->dateTime = $dateTime;
        $this->exportLogFactory = $exportLogFactory;
    }

    /**
     * Generate Kluwer XML export
     *
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    public function generateExport($fromDate, $toDate, $period = 'monthly')
    {
        try {
            // Get configuration
            $journalPrime = $this->helperData->getConfig('journal_prime');
            $accountMain = $this->helperData->getConfig('account_main');
            $accountSale = $this->helperData->getConfig('account_sale');
            $accountVat = $this->helperData->getConfig('account_vat');
            $accountRefund = $this->helperData->getConfig('account_refund');
            $accountEuSale = $this->helperData->getConfig('account_eu_sale');
            $exportPath = $this->helperData->getConfig('export_path');

            // Get invoices, credit memos and customers
            $invoices = $this->getInvoices($fromDate, $toDate);
            $creditMemos = $this->getCreditMemos($fromDate, $toDate);
            $customers = $this->getCustomers($fromDate, $toDate);

            if ($invoices->getSize() == 0 && $creditMemos->getSize() == 0) {
                return [
                    'success' => false,
                    'message' => __('No invoices or credit memos found for the selected date range.')
                ];
            }

            // Create period folder
            $periodFolder = $this->createPeriodFolder($fromDate, $period);
            $fullExportPath = $exportPath . $periodFolder . '/';

            // Generate Sales XML (Invoices + Credit Memos)
            $salesXml = $this->generateSalesXml($invoices, $creditMemos, $journalPrime, $accountMain, $accountSale, $accountVat, $accountRefund, $accountEuSale);
            $salesFilename = $this->saveXmlFile($salesXml, $fullExportPath, 'ImportSales.xml');

            // Generate Customers XML
            $customersXml = $this->generateCustomersXml($customers);
            $customersFilename = $this->saveXmlFile($customersXml, $fullExportPath, 'ImportCustomers.xml');

            // Log export
            $totalCount = $invoices->getSize() + $creditMemos->getSize();
            $this->logExport($periodFolder, $salesFilename, $customersFilename, $fromDate, $toDate, $totalCount);

            return [
                'success' => true,
                'sales_filename' => $salesFilename,
                'customers_filename' => $customersFilename,
                'period_folder' => $periodFolder,
                'message' => __('Export generated successfully')
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get invoices for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @return \Magento\Sales\Model\ResourceModel\Order\Invoice\Collection
     */
    protected function getInvoices($fromDate, $toDate)
    {
        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $collection = $objectManager->create(\Magento\Sales\Model\ResourceModel\Order\Invoice\Collection::class);

        if ($fromDate) {
            $collection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }

        if ($toDate) {
            $collection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }

        return $collection;
    }

    /**
     * Get credit memos for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @return \Magento\Sales\Model\ResourceModel\Order\Creditmemo\Collection
     */
    protected function getCreditMemos($fromDate, $toDate)
    {
        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $collection = $objectManager->create(\Magento\Sales\Model\ResourceModel\Order\Creditmemo\Collection::class);

        if ($fromDate) {
            $collection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }

        if ($toDate) {
            $collection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }

        return $collection;
    }

    /**
     * Get customers for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @return \Magento\Customer\Model\ResourceModel\Customer\Collection
     */
    protected function getCustomers($fromDate, $toDate)
    {
        $customerCollection = $this->customerCollectionFactory->create();

        if ($fromDate) {
            $customerCollection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }

        if ($toDate) {
            $customerCollection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }

        return $customerCollection;
    }

    /**
     * Create period folder name
     *
     * @param string $fromDate
     * @param string $period
     * @return string
     */
    protected function createPeriodFolder($fromDate, $period)
    {
        $date = new \DateTime($fromDate);

        if ($period === 'quarterly') {
            $quarter = ceil($date->format('n') / 3);
            return $date->format('Y') . 'Q' . $quarter;
        }

        // Default monthly
        return $date->format('Ym');
    }

    /**
     * Generate Sales XML content
     *
     * @param \Magento\Sales\Model\ResourceModel\Order\Invoice\Collection $invoices
     * @param \Magento\Sales\Model\ResourceModel\Order\Creditmemo\Collection $creditMemos
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     * @param string $accountRefund
     * @return string
     */
    protected function generateSalesXml($invoices, $creditMemos, $journalPrime, $accountMain, $accountSale, $accountVat, $accountRefund, $accountEuSale)
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><Import></Import>');

        // Add invoices
        foreach ($invoices as $invoice) {
            $this->addInvoiceToSalesXml($xml, $invoice, $journalPrime, $accountMain, $accountSale, $accountVat, $accountEuSale);
        }

        // Add credit memos (refunds)
        foreach ($creditMemos as $creditMemo) {
            $this->addCreditMemoToSalesXml($xml, $creditMemo, $journalPrime, $accountMain, $accountRefund, $accountVat, $accountEuSale);
        }

        // Format XML with proper indentation
        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->formatOutput = true;
        $dom->loadXML($xml->asXML());
        return $dom->saveXML();
    }



    /**
     * Generate Customers XML content
     *
     * @param \Magento\Customer\Model\ResourceModel\Customer\Collection $customers
     * @return string
     */
    protected function generateCustomersXml($customers)
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><Import></Import>');

        foreach ($customers as $customer) {
            $this->addCustomerToXml($xml, $customer);
        }

        // Format XML with proper indentation
        $dom = new \DOMDocument('1.0', 'UTF-8');
        $dom->formatOutput = true;
        $dom->loadXML($xml->asXML());
        return $dom->saveXML();
    }

    /**
     * Add invoice to Sales XML (Kluwer format)
     *
     * @param \SimpleXMLElement $xml
     * @param \Magento\Sales\Model\Order\Invoice $invoice
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     */
    protected function addInvoiceToSalesXml($xml, $invoice, $journalPrime, $accountMain, $accountSale, $accountVat, $accountEuSale)
    {
        $invoiceDate = date('Ymd', strtotime($invoice->getCreatedAt()));

        // Get order and customer info for EU check
        $order = $invoice->getOrder();
        $billingAddress = $order->getBillingAddress();
        $countryCode = $billingAddress->getCountryId();

        // Check if this is EU customer (tax amount is zero in invoice)
        $vatAmount = $invoice->getTaxAmount();
        $isEuCustomer = ($vatAmount == 0 && $countryCode !== 'BE');

        // Main debit entry
        $entryNode = $xml->addChild('Entry');
        $entryNode->addChild('Journal', $journalPrime);
        $entryNode->addChild('Date', $invoiceDate);
        $entryNode->addChild('Account', $accountMain);
        $entryNode->addChild('DocType', '10');
        $entryNode->addChild('DebCre', '1');
        $entryNode->addChild('Amount', number_format($invoice->getGrandTotal(), 2, '.', ''));
        $entryNode->addChild('Description', htmlspecialchars('Invoice #' . $invoice->getIncrementId()));
        $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
        $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
        $entryNode->addChild('Ref', '0');
        $entryNode->addChild('Unit', '0');

        // Sales credit entry - Use EU account for EU customers (zero VAT)
        $salesAmount = $invoice->getSubtotal();
        if ($salesAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $invoiceDate);
            // Use EU sales account for EU customers, regular sales account for others
            $salesAccount = $isEuCustomer ? $accountEuSale : $accountSale;
            $entryNode->addChild('Account', $salesAccount);
            $entryNode->addChild('DocType', '10');
            $entryNode->addChild('DebCre', '-1');
            $entryNode->addChild('Amount', number_format($salesAmount, 2, '.', ''));
            $description = $isEuCustomer ? 'EU Sales Invoice #' : 'Sales Invoice #';
            $entryNode->addChild('Description', htmlspecialchars($description . $invoice->getIncrementId()));
            $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
            $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }

        // VAT credit entry - Only add if VAT amount > 0 (Belgian customers)
        if ($vatAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $invoiceDate);
            $entryNode->addChild('Account', $accountVat);
            $entryNode->addChild('DocType', '10');
            $entryNode->addChild('DebCre', '-1');
            $entryNode->addChild('Amount', number_format($vatAmount, 2, '.', ''));
            $entryNode->addChild('Description', htmlspecialchars('VAT Invoice #' . $invoice->getIncrementId()));
            $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
            $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }
    }

    /**
     * Add credit memo to Sales XML (Kluwer format)
     *
     * @param \SimpleXMLElement $xml
     * @param \Magento\Sales\Model\Order\Creditmemo $creditMemo
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountRefund
     * @param string $accountVat
     */
    protected function addCreditMemoToSalesXml($xml, $creditMemo, $journalPrime, $accountMain, $accountRefund, $accountVat, $accountEuSale)
    {
        $creditMemoDate = date('Ymd', strtotime($creditMemo->getCreatedAt()));

        // Get order and customer info for EU check
        $order = $creditMemo->getOrder();
        $billingAddress = $order->getBillingAddress();
        $countryCode = $billingAddress->getCountryId();

        // Check if this is EU customer (tax amount is zero in credit memo)
        $vatAmount = $creditMemo->getTaxAmount();
        $isEuCustomer = ($vatAmount == 0 && $countryCode !== 'BE');

        // Main credit entry (opposite of invoice)
        $entryNode = $xml->addChild('Entry');
        $entryNode->addChild('Journal', $journalPrime);
        $entryNode->addChild('Date', $creditMemoDate);
        $entryNode->addChild('Account', $accountMain);
        $entryNode->addChild('DocType', '20');
        $entryNode->addChild('DebCre', '-1');
        $entryNode->addChild('Amount', number_format($creditMemo->getGrandTotal(), 2, '.', ''));
        $entryNode->addChild('Description', htmlspecialchars('Credit Memo #' . $creditMemo->getIncrementId()));
        $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
        $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
        $entryNode->addChild('Ref', '0');
        $entryNode->addChild('Unit', '0');

        // Refund debit entry - Use EU account for EU customers
        $refundAmount = $creditMemo->getSubtotal();
        if ($refundAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $creditMemoDate);
            // Use EU sales account for EU customers, regular refund account for others
            $refundAccount = $isEuCustomer ? $accountEuSale : $accountRefund;
            $entryNode->addChild('Account', $refundAccount);
            $entryNode->addChild('DocType', '20');
            $entryNode->addChild('DebCre', '1');
            $entryNode->addChild('Amount', number_format($refundAmount, 2, '.', ''));
            $description = $isEuCustomer ? 'EU Refund Credit Memo #' : 'Refund Credit Memo #';
            $entryNode->addChild('Description', htmlspecialchars($description . $creditMemo->getIncrementId()));
            $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
            $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }

        // VAT debit entry - Only add if VAT amount > 0 (Belgian customers)
        if ($vatAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $creditMemoDate);
            $entryNode->addChild('Account', $accountVat);
            $entryNode->addChild('DocType', '20');
            $entryNode->addChild('DebCre', '1');
            $entryNode->addChild('Amount', number_format($vatAmount, 2, '.', ''));
            $entryNode->addChild('Description', htmlspecialchars('VAT Refund Credit Memo #' . $creditMemo->getIncrementId()));
            $entryNode->addChild('CustomerCode', htmlspecialchars($order->getCustomerId() ?: 'GUEST'));
            $entryNode->addChild('CustomerName', htmlspecialchars($billingAddress->getFirstname() . ' ' . $billingAddress->getLastname()));
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }
    }

    /**
     * Add customer to Customers XML (Kluwer format)
     *
     * @param \SimpleXMLElement $xml
     * @param \Magento\Customer\Model\Customer $customer
     */
    protected function addCustomerToXml($xml, $customer)
    {
        $customerNode = $xml->addChild('Customer');
        $customerNode->addChild('Code', htmlspecialchars($customer->getId()));
        $customerNode->addChild('Name', htmlspecialchars($customer->getFirstname() . ' ' . $customer->getLastname()));
        $customerNode->addChild('Email', htmlspecialchars($customer->getEmail()));

        // Get default billing address
        $billingAddress = $customer->getDefaultBillingAddress();
        if ($billingAddress) {
            $street = $billingAddress->getStreet();
            $customerNode->addChild('Address', htmlspecialchars(is_array($street) ? implode(', ', $street) : $street));
            $customerNode->addChild('City', htmlspecialchars($billingAddress->getCity() ?: ''));
            $customerNode->addChild('PostalCode', htmlspecialchars($billingAddress->getPostcode() ?: ''));
            $customerNode->addChild('Country', htmlspecialchars($billingAddress->getCountryId() ?: ''));
            $customerNode->addChild('Phone', htmlspecialchars($billingAddress->getTelephone() ?: ''));
        } else {
            $customerNode->addChild('Address', '');
            $customerNode->addChild('City', '');
            $customerNode->addChild('PostalCode', '');
            $customerNode->addChild('Country', '');
            $customerNode->addChild('Phone', '');
        }

        $customerNode->addChild('CreatedAt', date('Ymd', strtotime($customer->getCreatedAt())));
    }

    /**
     * Save XML file
     *
     * @param string $xml
     * @param string $exportPath
     * @param string $filename
     * @return string
     */
    protected function saveXmlFile($xml, $exportPath, $filename)
    {
        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);

        // Create directory if it doesn't exist
        if (!$mediaDirectory->isDirectory($exportPath)) {
            $mediaDirectory->create($exportPath);
        }

        $filePath = $exportPath . $filename;
        $mediaDirectory->writeFile($filePath, $xml);

        return $filename;
    }

    /**
     * Log export to database
     *
     * @param string $period
     * @param string $salesFilename
     * @param string $customersFilename
     * @param string $fromDate
     * @param string $toDate
     * @param int $ordersCount
     */
    protected function logExport($period, $salesFilename, $customersFilename, $fromDate, $toDate, $ordersCount)
    {
        $exportLog = $this->exportLogFactory->create();
        $exportLog->setData([
            'period' => $period,
            'filename' => $salesFilename . ', ' . $customersFilename,
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'orders_count' => $ordersCount
        ]);
        $exportLog->save();
    }
}
