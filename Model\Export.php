<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Helper\Data as HelperData;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Ma<PERSON>ya<PERSON>lim\KluwerExport\Model\ExportLogFactory;

class Export extends AbstractModel
{
    /**
     * @var OrderCollectionFactory
     */
    protected $orderCollectionFactory;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var HelperData
     */
    protected $helperData;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var ExportLogFactory
     */
    protected $exportLogFactory;

    /**
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param Filesystem $filesystem
     * @param HelperData $helperData
     * @param DateTime $dateTime
     * @param ExportLogFactory $exportLogFactory
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        Filesystem $filesystem,
        HelperData $helperData,
        DateTime $dateTime,
        ExportLogFactory $exportLogFactory
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->filesystem = $filesystem;
        $this->helperData = $helperData;
        $this->dateTime = $dateTime;
        $this->exportLogFactory = $exportLogFactory;
    }

    /**
     * Generate Kluwer XML export
     *
     * @param string $fromDate
     * @param string $toDate
     * @return array
     */
    public function generateExport($fromDate, $toDate)
    {
        try {
            // Get configuration
            $journalPrime = $this->helperData->getConfig('journal_prime');
            $accountMain = $this->helperData->getConfig('account_main');
            $accountSale = $this->helperData->getConfig('account_sale');
            $accountVat = $this->helperData->getConfig('account_vat');
            $orderStatus = $this->helperData->getConfig('order_status');
            $exportPath = $this->helperData->getConfig('export_path');

            // Get orders
            $orders = $this->getOrders($fromDate, $toDate, $orderStatus);

            if ($orders->getSize() == 0) {
                return [
                    'success' => false,
                    'message' => __('No orders found for the selected date range.')
                ];
            }

            // Generate XML
            $xml = $this->generateXml($orders, $journalPrime, $accountMain, $accountSale, $accountVat);

            // Save file
            $filename = $this->saveXmlFile($xml, $exportPath, $fromDate, $toDate);

            return [
                'success' => true,
                'filename' => $filename,
                'message' => __('Export generated successfully')
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get orders for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @param string $status
     * @return \Magento\Sales\Model\ResourceModel\Order\Collection
     */
    protected function getOrders($fromDate, $toDate, $status)
    {
        $collection = $this->orderCollectionFactory->create();
        
        if ($fromDate) {
            $collection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }
        
        if ($toDate) {
            $collection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }
        
        if ($status) {
            $collection->addFieldToFilter('status', $status);
        }

        return $collection;
    }

    /**
     * Generate XML content
     *
     * @param \Magento\Sales\Model\ResourceModel\Order\Collection $orders
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     * @return string
     */
    protected function generateXml($orders, $journalPrime, $accountMain, $accountSale, $accountVat)
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><KluwerExport></KluwerExport>');
        
        $journalNode = $xml->addChild('Journal');
        $journalNode->addAttribute('Prime', $journalPrime);
        
        foreach ($orders as $order) {
            $this->addOrderToXml($journalNode, $order, $accountMain, $accountSale, $accountVat);
        }

        return $xml->asXML();
    }

    /**
     * Add order to XML
     *
     * @param \SimpleXMLElement $journalNode
     * @param \Magento\Sales\Model\Order $order
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     */
    protected function addOrderToXml($journalNode, $order, $accountMain, $accountSale, $accountVat)
    {
        $transactionNode = $journalNode->addChild('Transaction');
        $transactionNode->addAttribute('Date', date('Y-m-d', strtotime($order->getCreatedAt())));
        $transactionNode->addAttribute('Reference', $order->getIncrementId());
        
        // Main account entry (debit)
        $entryNode = $transactionNode->addChild('Entry');
        $entryNode->addAttribute('Account', $accountMain);
        $entryNode->addAttribute('Debit', number_format($order->getGrandTotal(), 2, '.', ''));
        $entryNode->addAttribute('Description', 'Order #' . $order->getIncrementId());
        
        // Sales account entry (credit)
        $salesAmount = $order->getSubtotal();
        if ($salesAmount > 0) {
            $entryNode = $transactionNode->addChild('Entry');
            $entryNode->addAttribute('Account', $accountSale);
            $entryNode->addAttribute('Credit', number_format($salesAmount, 2, '.', ''));
            $entryNode->addAttribute('Description', 'Sales Order #' . $order->getIncrementId());
        }
        
        // VAT account entry (credit)
        $vatAmount = $order->getTaxAmount();
        if ($vatAmount > 0) {
            $entryNode = $transactionNode->addChild('Entry');
            $entryNode->addAttribute('Account', $accountVat);
            $entryNode->addAttribute('Credit', number_format($vatAmount, 2, '.', ''));
            $entryNode->addAttribute('Description', 'VAT Order #' . $order->getIncrementId());
        }
    }

    /**
     * Save XML file
     *
     * @param string $xml
     * @param string $exportPath
     * @param string $fromDate
     * @param string $toDate
     * @return string
     */
    protected function saveXmlFile($xml, $exportPath, $fromDate, $toDate)
    {
        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        
        // Create directory if it doesn't exist
        if (!$mediaDirectory->isDirectory($exportPath)) {
            $mediaDirectory->create($exportPath);
        }
        
        $filename = 'kluwer_export_' . date('Y-m-d_H-i-s') . '.xml';
        $filePath = $exportPath . $filename;
        
        $mediaDirectory->writeFile($filePath, $xml);
        
        return $filename;
    }
}
