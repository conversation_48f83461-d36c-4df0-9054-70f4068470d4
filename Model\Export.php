<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Helper\Data as HelperData;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Ma<PERSON>ya<PERSON>lim\KluwerExport\Model\ExportLogFactory;

class Export extends AbstractModel
{
    /**
     * @var OrderCollectionFactory
     */
    protected $orderCollectionFactory;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var HelperData
     */
    protected $helperData;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var ExportLogFactory
     */
    protected $exportLogFactory;

    /**
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param Filesystem $filesystem
     * @param HelperData $helperData
     * @param DateTime $dateTime
     * @param ExportLogFactory $exportLogFactory
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        Filesystem $filesystem,
        HelperData $helperData,
        DateTime $dateTime,
        ExportLogFactory $exportLogFactory
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->filesystem = $filesystem;
        $this->helperData = $helperData;
        $this->dateTime = $dateTime;
        $this->exportLogFactory = $exportLogFactory;
    }

    /**
     * Generate Kluwer XML export
     *
     * @param string $fromDate
     * @param string $toDate
     * @param string $period
     * @return array
     */
    public function generateExport($fromDate, $toDate, $period = 'monthly')
    {
        try {
            // Get configuration
            $journalPrime = $this->helperData->getConfig('journal_prime');
            $accountMain = $this->helperData->getConfig('account_main');
            $accountSale = $this->helperData->getConfig('account_sale');
            $accountVat = $this->helperData->getConfig('account_vat');
            $orderStatus = $this->helperData->getConfig('order_status');
            $exportPath = $this->helperData->getConfig('export_path');

            // Get orders and customers
            $orders = $this->getOrders($fromDate, $toDate, $orderStatus);
            $customers = $this->getCustomers($fromDate, $toDate);

            if ($orders->getSize() == 0) {
                return [
                    'success' => false,
                    'message' => __('No orders found for the selected date range.')
                ];
            }

            // Create period folder
            $periodFolder = $this->createPeriodFolder($fromDate, $period);
            $fullExportPath = $exportPath . $periodFolder . '/';

            // Generate Sales XML
            $salesXml = $this->generateSalesXml($orders, $journalPrime, $accountMain, $accountSale, $accountVat);
            $salesFilename = $this->saveXmlFile($salesXml, $fullExportPath, 'ImportSales.xml');

            // Generate Customers XML
            $customersXml = $this->generateCustomersXml($customers);
            $customersFilename = $this->saveXmlFile($customersXml, $fullExportPath, 'ImportCustomers.xml');

            // Log export
            $this->logExport($periodFolder, $salesFilename, $customersFilename, $fromDate, $toDate, $orders->getSize());

            return [
                'success' => true,
                'sales_filename' => $salesFilename,
                'customers_filename' => $customersFilename,
                'period_folder' => $periodFolder,
                'message' => __('Export generated successfully')
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get orders for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @param string $status
     * @return \Magento\Sales\Model\ResourceModel\Order\Collection
     */
    protected function getOrders($fromDate, $toDate, $status)
    {
        $collection = $this->orderCollectionFactory->create();

        if ($fromDate) {
            $collection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }

        if ($toDate) {
            $collection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }

        if ($status) {
            $collection->addFieldToFilter('status', $status);
        }

        return $collection;
    }

    /**
     * Get customers for export
     *
     * @param string $fromDate
     * @param string $toDate
     * @return \Magento\Customer\Model\ResourceModel\Customer\Collection
     */
    protected function getCustomers($fromDate, $toDate)
    {
        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $customerCollection = $objectManager->create(\Magento\Customer\Model\ResourceModel\Customer\Collection::class);

        if ($fromDate) {
            $customerCollection->addFieldToFilter('created_at', ['gteq' => $fromDate . ' 00:00:00']);
        }

        if ($toDate) {
            $customerCollection->addFieldToFilter('created_at', ['lteq' => $toDate . ' 23:59:59']);
        }

        return $customerCollection;
    }

    /**
     * Create period folder name
     *
     * @param string $fromDate
     * @param string $period
     * @return string
     */
    protected function createPeriodFolder($fromDate, $period)
    {
        $date = new \DateTime($fromDate);

        if ($period === 'quarterly') {
            $quarter = ceil($date->format('n') / 3);
            return $date->format('Y') . 'Q' . $quarter;
        }

        // Default monthly
        return $date->format('Ym');
    }

    /**
     * Generate Sales XML content
     *
     * @param \Magento\Sales\Model\ResourceModel\Order\Collection $orders
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     * @return string
     */
    protected function generateSalesXml($orders, $journalPrime, $accountMain, $accountSale, $accountVat)
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><Import></Import>');

        foreach ($orders as $order) {
            $this->addOrderToSalesXml($xml, $order, $journalPrime, $accountMain, $accountSale, $accountVat);
        }

        return $xml->asXML();
    }

    /**
     * Generate Customers XML content
     *
     * @param \Magento\Customer\Model\ResourceModel\Customer\Collection $customers
     * @return string
     */
    protected function generateCustomersXml($customers)
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><Import></Import>');

        foreach ($customers as $customer) {
            $this->addCustomerToXml($xml, $customer);
        }

        return $xml->asXML();
    }

    /**
     * Add order to Sales XML (Kluwer format)
     *
     * @param \SimpleXMLElement $xml
     * @param \Magento\Sales\Model\Order $order
     * @param string $journalPrime
     * @param string $accountMain
     * @param string $accountSale
     * @param string $accountVat
     */
    protected function addOrderToSalesXml($xml, $order, $journalPrime, $accountMain, $accountSale, $accountVat)
    {
        $invoiceDate = date('Ymd', strtotime($order->getCreatedAt()));

        // Main debit entry
        $entryNode = $xml->addChild('Entry');
        $entryNode->addChild('Journal', $journalPrime);
        $entryNode->addChild('Date', $invoiceDate);
        $entryNode->addChild('Account', $accountMain);
        $entryNode->addChild('DocType', '10');
        $entryNode->addChild('DebCre', '1');
        $entryNode->addChild('Amount', number_format($order->getGrandTotal(), 2, '.', ''));
        $entryNode->addChild('Description', 'Order #' . $order->getIncrementId());
        $entryNode->addChild('Ref', '0');
        $entryNode->addChild('Unit', '0');

        // Sales credit entry
        $salesAmount = $order->getSubtotal();
        if ($salesAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $invoiceDate);
            $entryNode->addChild('Account', $accountSale);
            $entryNode->addChild('DocType', '10');
            $entryNode->addChild('DebCre', '-1');
            $entryNode->addChild('Amount', number_format($salesAmount, 2, '.', ''));
            $entryNode->addChild('Description', 'Sales Order #' . $order->getIncrementId());
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }

        // VAT credit entry
        $vatAmount = $order->getTaxAmount();
        if ($vatAmount > 0) {
            $entryNode = $xml->addChild('Entry');
            $entryNode->addChild('Journal', $journalPrime);
            $entryNode->addChild('Date', $invoiceDate);
            $entryNode->addChild('Account', $accountVat);
            $entryNode->addChild('DocType', '10');
            $entryNode->addChild('DebCre', '-1');
            $entryNode->addChild('Amount', number_format($vatAmount, 2, '.', ''));
            $entryNode->addChild('Description', 'VAT Order #' . $order->getIncrementId());
            $entryNode->addChild('Ref', '0');
            $entryNode->addChild('Unit', '0');
        }
    }

    /**
     * Add customer to Customers XML (Kluwer format)
     *
     * @param \SimpleXMLElement $xml
     * @param \Magento\Customer\Model\Customer $customer
     */
    protected function addCustomerToXml($xml, $customer)
    {
        $customerNode = $xml->addChild('Customer');
        $customerNode->addChild('Code', $customer->getId());
        $customerNode->addChild('Name', $customer->getFirstname() . ' ' . $customer->getLastname());
        $customerNode->addChild('Email', $customer->getEmail());

        // Get default billing address
        $billingAddress = $customer->getDefaultBillingAddress();
        if ($billingAddress) {
            $customerNode->addChild('Address', implode(', ', $billingAddress->getStreet()));
            $customerNode->addChild('City', $billingAddress->getCity());
            $customerNode->addChild('PostalCode', $billingAddress->getPostcode());
            $customerNode->addChild('Country', $billingAddress->getCountryId());
            $customerNode->addChild('Phone', $billingAddress->getTelephone());
        }

        $customerNode->addChild('CreatedAt', date('Ymd', strtotime($customer->getCreatedAt())));
    }

    /**
     * Save XML file
     *
     * @param string $xml
     * @param string $exportPath
     * @param string $filename
     * @return string
     */
    protected function saveXmlFile($xml, $exportPath, $filename)
    {
        $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);

        // Create directory if it doesn't exist
        if (!$mediaDirectory->isDirectory($exportPath)) {
            $mediaDirectory->create($exportPath);
        }

        $filePath = $exportPath . $filename;
        $mediaDirectory->writeFile($filePath, $xml);

        return $filename;
    }

    /**
     * Log export to database
     *
     * @param string $period
     * @param string $salesFilename
     * @param string $customersFilename
     * @param string $fromDate
     * @param string $toDate
     * @param int $ordersCount
     */
    protected function logExport($period, $salesFilename, $customersFilename, $fromDate, $toDate, $ordersCount)
    {
        $exportLog = $this->exportLogFactory->create();
        $exportLog->setData([
            'period' => $period,
            'filename' => $salesFilename . ', ' . $customersFilename,
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'orders_count' => $ordersCount
        ]);
        $exportLog->save();
    }
}
