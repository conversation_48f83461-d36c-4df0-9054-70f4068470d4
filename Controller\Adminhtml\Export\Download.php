<?php

namespace Ma<PERSON>ya<PERSON><PERSON>\KluwerExport\Controller\Adminhtml\Export;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Controller\Result\Raw;
use Magento\Framework\Controller\Result\RawFactory;

class Download extends Action
{
    /**
     * @var FileFactory
     */
    protected $fileFactory;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var RawFactory
     */
    protected $rawFactory;

    /**
     * @param Context $context
     * @param FileFactory $fileFactory
     * @param Filesystem $filesystem
     * @param RawFactory $rawFactory
     */
    public function __construct(
        Context $context,
        FileFactory $fileFactory,
        Filesystem $filesystem,
        RawFactory $rawFactory
    ) {
        $this->fileFactory = $fileFactory;
        $this->filesystem = $filesystem;
        $this->rawFactory = $rawFactory;
        parent::__construct($context);
    }

    /**
     * Download export file
     *
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function execute()
    {
        $filename = $this->getRequest()->getParam('file');
        $period = $this->getRequest()->getParam('period');
        
        if (!$filename || !$period) {
            $this->messageManager->addErrorMessage(__('Invalid file or period specified.'));
            return $this->_redirect('*/*/index');
        }

        try {
            $mediaDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
            $filePath = 'kluwer_exports/' . $period . '/' . $filename;
            
            if (!$mediaDirectory->isFile($filePath)) {
                $this->messageManager->addErrorMessage(__('File not found: %1', $filename));
                return $this->_redirect('*/*/index');
            }

            $fileContent = $mediaDirectory->readFile($filePath);
            
            return $this->fileFactory->create(
                $filename,
                $fileContent,
                DirectoryList::VAR_DIR,
                'application/xml'
            );

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Error downloading file: %1', $e->getMessage()));
            return $this->_redirect('*/*/index');
        }
    }

    /**
     * Check ACL permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Mageyazilim_KluwerExport::export_download');
    }
}
