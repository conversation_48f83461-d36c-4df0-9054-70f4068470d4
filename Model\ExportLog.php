<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Model;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\DataObject\IdentityInterface;

class ExportLog extends AbstractModel implements IdentityInterface
{
    /**
     * Cache tag
     */
    const CACHE_TAG = 'kluwer_export_log';

    /**
     * Cache tag
     *
     * @var string
     */
    protected $_cacheTag = 'kluwer_export_log';

    /**
     * Event prefix
     *
     * @var string
     */
    protected $_eventPrefix = 'kluwer_export_log';

    /**
     * Initialize model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(\Mageyazilim\KluwerExport\Model\ResourceModel\ExportLog::class);
    }

    /**
     * Return unique ID(s) for each object in system
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Get default values
     *
     * @return array
     */
    public function getDefaultValues()
    {
        $values = [];
        return $values;
    }
}
