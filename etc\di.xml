<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Mageyazilim\KluwerExport\Model\Export">
        <arguments>
            <argument name="orderCollectionFactory" xsi:type="object">Magento\Sales\Model\ResourceModel\Order\CollectionFactory</argument>
            <argument name="customerCollectionFactory" xsi:type="object">Magento\Customer\Model\ResourceModel\Customer\CollectionFactory</argument>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem</argument>
            <argument name="helperData" xsi:type="object">Mageyazilim\KluwerExport\Helper\Data</argument>
            <argument name="dateTime" xsi:type="object">Magento\Framework\Stdlib\DateTime\DateTime</argument>
            <argument name="exportLogFactory" xsi:type="object">Mageyazilim\KluwerExport\Model\ExportLogFactory</argument>
        </arguments>
    </type>

    <!-- Grid Data Provider -->
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="kluwer_export_logs_listing_data_source" xsi:type="string">Mageyazilim\KluwerExport\Model\ResourceModel\ExportLog\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
</config>
