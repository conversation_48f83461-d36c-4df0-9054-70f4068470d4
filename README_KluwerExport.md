
# KluwerExport Magento 2 Module

## 📦 Module Overview
`KluwerExport` by `<PERSON><PERSON>ya<PERSON><PERSON>` allows exporting **sales invoices** and **customer data** from Magento 2 into XML format compatible with **Kluwer accounting software**.

---

## ✅ Features

- Export sales (invoices and credit memos) to `ImportSales.xml`
- Export customers to `ImportCustomers.xml`
- Export by selected period (monthly or quarterly)
- Kluwer-compatible structure (based on provided XSD & documentation)
- Automatic VAT logic:
  - BE + no VAT → VAT included (Ventil 0)
  - BE + with VAT → B2B VAT excluded (Ventil 4)
  - EU (non-BE) + with VAT → B2B VAT excluded (Ventil 51)
  - EU (non-BE) + no VAT → B2C VAT included (Ventil 0)
- Avoids duplicate customer export using `kluwer_exported` customer attribute
- Tracks all exports in an admin grid
- XML download from the admin panel
- Unique `DocNumber` per invoice/credit memo using increment_id

---

## 📁 Folder Structure

```
Mageyazilim/KluwerExport/
├── Controller/
│   └── Adminhtml/Export/...
├── Model/...
├── View/
│   └── adminhtml/templates/...
├── etc/
│   ├── acl.xml
│   ├── adminhtml/
│   │   └── menu.xml
│   └── module.xml
├── Setup/
│   └── Patch/Data/AddCustomerAttribute.php
├── registration.php
└── composer.json
```

---

## 🛠️ How It Works

1. Navigate to **Admin > Kluwer Export**
2. Choose period: `Monthly` or `Quarterly`
3. Exports are generated into:
   - `var/kluwer_exports/YYYYMM/ImportSales.xml`
   - `var/kluwer_exports/YYYYMM/ImportCustomers.xml`
4. Downloads are available from the export grid

---

## 🔢 Critical Logic Points

- `VATCode/VATStatus/VATNumber` are populated based on presence and format
- `Ventil` is dynamically set based on:
  - Country
  - VAT number presence
- Customers are exported **only once** per period
- Multiple sales for the same customer share one `<Customer>` but multiple `<Sale>` blocks
- `DocNumber` is unique and reflects the invoice increment ID
- `OurRef` = invoice increment ID, `YourRef` is left empty

---

## 🧾 Accounting Field Mapping

| Field             | Source                                       |
|------------------|----------------------------------------------|
| Prime            | customer entity_id                           |
| Name             | customer billing name                        |
| Country          | billing address country                      |
| VATNumber        | normalized VAT or empty                      |
| Street/House     | split from full street using smart parser    |
| VATAmount        | from invoice data                            |
| AccountingPeriod | from selected period                         |
| VATMonth         | YYYYMM of invoice date                       |
| Journal_Prime    | Always `1`                                   |
| DocType          | Always `10`                                  |
| DebCre           | 1 for customer, -1 for income/VAT lines      |
| Ref/Unit         | Always `0`                                   |

---

## 📂 Export Sample Paths

```
var/kluwer_exports/202501/ImportCustomers.xml
var/kluwer_exports/202501/ImportSales.xml
```

---

## 🧩 Compatibility

- Magento 2.4.x
- PHP 7.4, 8.1

---

## 🧪 Testing

- Validated against Kluwer XML schema (Sales.xsd, Customers.xsd)
- Passed all critical test cases and real-life samples
- Credit memos included
- Multiple invoices per customer supported

---

## 🧼 Notes

- VAT numbers are normalized (e.g., stripped dots/spaces)
- Street/house number logic uses regex to extract cleanly
- Status: 1 = newly exported, 0 = already exported

---

## 🏁 To-Do

- Add CLI command support (optional)
- Logging and error report interface (optional)

---

Module developed by **Mageyazilim**.
