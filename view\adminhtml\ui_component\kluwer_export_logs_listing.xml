<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">kluwer_export_logs_listing.kluwer_export_logs_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <buttons>
            <button name="back">
                <url path="kluwerexport/export/index"/>
                <class>back</class>
                <label translate="true">Back to Export</label>
            </button>
        </buttons>
        <spinner>kluwer_export_logs_columns</spinner>
        <deps>
            <dep>kluwer_export_logs_listing.kluwer_export_logs_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="kluwer_export_logs_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">log_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>Mageyazilim_KluwerExport::export_logs</aclResource>
        <dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="kluwer_export_logs_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>log_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
        </filters>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="kluwer_export_logs_columns">
        <settings>
            <editorConfig>
                <param name="clientConfig" xsi:type="array">
                    <item name="saveUrl" xsi:type="url" path="kluwerexport/export/inlineEdit"/>
                    <item name="validateBeforeSave" xsi:type="boolean">false</item>
                </param>
                <param name="indexField" xsi:type="string">log_id</param>
                <param name="enabled" xsi:type="boolean">true</param>
                <param name="selectProvider" xsi:type="string">kluwer_export_logs_listing.kluwer_export_logs_listing.kluwer_export_logs_columns.ids</param>
            </editorConfig>
            <childDefaults>
                <param name="fieldAction" xsi:type="array">
                    <item name="provider" xsi:type="string">kluwer_export_logs_listing.kluwer_export_logs_listing.kluwer_export_logs_columns_editor</item>
                    <item name="target" xsi:type="string">startEdit</item>
                    <item name="params" xsi:type="array">
                        <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
                        <item name="1" xsi:type="boolean">true</item>
                    </item>
                </param>
            </childDefaults>
        </settings>
        <selectionsColumn name="ids">
            <settings>
                <indexField>log_id</indexField>
            </settings>
        </selectionsColumn>
        <column name="log_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="period_folder">
            <settings>
                <filter>text</filter>
                <label translate="true">Period</label>
            </settings>
        </column>
        <column name="sales_filename">
            <settings>
                <filter>text</filter>
                <label translate="true">Sales File</label>
            </settings>
        </column>
        <column name="customers_filename">
            <settings>
                <filter>text</filter>
                <label translate="true">Customers File</label>
            </settings>
        </column>
        <column name="from_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">From Date</label>
            </settings>
        </column>
        <column name="to_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">To Date</label>
            </settings>
        </column>
        <column name="record_count">
            <settings>
                <filter>textRange</filter>
                <label translate="true">Records</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created At</label>
            </settings>
        </column>
        <actionsColumn name="actions" class="Mageyazilim\KluwerExport\Ui\Component\Listing\Column\Actions">
            <settings>
                <indexField>log_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>
