define([
    'j<PERSON>y',
    'mage/url',
    'Magento_Ui/js/modal/alert',
    'mage/translate'
], function ($, urlBuilder, alert, $t) {
    'use strict';

    return function (config, element) {
        $(element).on('click', function () {
            var fromDate = $('#from_date').val();
            var toDate = $('#to_date').val();
            
            if (!fromDate || !toDate) {
                alert({
                    title: $t('Error'),
                    content: $t('Please select both from and to dates.')
                });
                return;
            }
            
            if (fromDate > toDate) {
                alert({
                    title: $t('Error'),
                    content: $t('From date cannot be greater than to date.')
                });
                return;
            }
            
            // Show loading
            $(element).prop('disabled', true).text($t('Generating...'));
            $('#export-result').hide();
            
            $.ajax({
                url: config.url,
                type: 'POST',
                dataType: 'json',
                data: {
                    from_date: fromDate,
                    to_date: toDate,
                    form_key: window.FORM_KEY
                },
                success: function (response) {
                    $(element).prop('disabled', false).text($t('Generate Export'));
                    
                    if (response.success) {
                        $('#export-result .message').html(
                            '<div class="message message-success">' +
                            '<div>' + response.message + '</div>' +
                            '<div><strong>' + $t('Filename: ') + response.filename + '</strong></div>' +
                            '</div>'
                        );
                    } else {
                        $('#export-result .message').html(
                            '<div class="message message-error">' +
                            '<div>' + response.message + '</div>' +
                            '</div>'
                        );
                    }
                    
                    $('#export-result').show();
                },
                error: function () {
                    $(element).prop('disabled', false).text($t('Generate Export'));
                    $('#export-result .message').html(
                        '<div class="message message-error">' +
                        '<div>' + $t('An error occurred while generating the export.') + '</div>' +
                        '</div>'
                    );
                    $('#export-result').show();
                }
            });
        });
    };
});
