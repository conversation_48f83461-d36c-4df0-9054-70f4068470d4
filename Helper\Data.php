<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    const XML_PATH = 'kluwerexport/settings/';

    public function getConfig($field, $storeId = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH . $field,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
