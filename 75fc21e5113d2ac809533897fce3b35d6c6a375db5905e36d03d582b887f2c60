{"0": "Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336", "1": "#1 Magento\\Framework\\Acl\\Builder\\Proxy->getAcl() called at [vendor/magento/module-backend/Model/Auth/Session.php:132]\n#2 Magento\\Backend\\Model\\Auth\\Session->refreshAcl() called at [vendor/magento/module-backend/App/Action/Plugin/Authentication.php:144]\n#3 Magento\\Backend\\App\\Action\\Plugin\\Authentication->aroundDispatch() called at [vendor/magento/framework/Interception/Interceptor.php:135]\n#4 Magento\\Backend\\Controller\\Adminhtml\\Index\\Index\\Interceptor->Magento\\Framework\\Interception\\{closure}() called at [vendor/magento/framework/Interception/Interceptor.php:153]\n#5 Magento\\Backend\\Controller\\Adminhtml\\Index\\Index\\Interceptor->___callPlugins() called at [generated/code/Magento/Backend/Controller/Adminhtml/Index/Index/Interceptor.php:32]\n#6 Magento\\Backend\\Controller\\Adminhtml\\Index\\Index\\Interceptor->dispatch() called at [vendor/magento/framework/App/FrontController.php:245]\n#7 Magento\\Framework\\App\\FrontController->getActionResponse() called at [vendor/magento/framework/App/FrontController.php:212]\n#8 Magento\\Framework\\App\\FrontController->processRequest() called at [vendor/magento/framework/App/FrontController.php:147]\n#9 Magento\\Framework\\App\\FrontController->dispatch() called at [vendor/magento/framework/Interception/Interceptor.php:58]\n#10 Magento\\Framework\\App\\FrontController\\Interceptor->___callParent() called at [vendor/magento/framework/Interception/Interceptor.php:138]\n#11 Magento\\Framework\\App\\FrontController\\Interceptor->Magento\\Framework\\Interception\\{closure}() called at [vendor/magento/framework/Interception/Interceptor.php:153]\n#12 Magento\\Framework\\App\\FrontController\\Interceptor->___callPlugins() called at [generated/code/Magento/Framework/App/FrontController/Interceptor.php:23]\n#13 Magento\\Framework\\App\\FrontController\\Interceptor->dispatch() called at [vendor/magento/framework/App/Http.php:116]\n#14 Magento\\Framework\\App\\Http->launch() called at [generated/code/Magento/Framework/App/Http/Interceptor.php:23]\n#15 Magento\\Framework\\App\\Http\\Interceptor->launch() called at [vendor/magento/framework/App/Bootstrap.php:264]\n#16 Magento\\Framework\\App\\Bootstrap->run() called at [pub/index.php:30]\n", "url": "/admin_q44mbi/", "script_name": "/index.php", "report_id": "75fc21e5113d2ac809533897fce3b35d6c6a375db5905e36d03d582b887f2c60"}