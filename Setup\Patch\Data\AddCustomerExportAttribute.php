<?php
namespace Ma<PERSON><PERSON><PERSON><PERSON>\KluwerExport\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Customer\Model\Customer;

class AddCustomerExportAttribute implements DataPatchInterface
{
    private $moduleDataSetup;
    private $customerSetupFactory;
    private $eavSetupFactory;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        CustomerSetupFactory $customerSetupFactory,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->customerSetupFactory = $customerSetupFactory;
        $this->eavSetupFactory = $eavSetupFactory;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $customerSetup->addAttribute(Customer::ENTITY, 'kluwer_exported', [
            'type'         => 'int',
            'label'        => 'Exported to Kluwer',
            'input'        => 'boolean',
            'required'     => false,
            'visible'      => true,
            'user_defined' => true,
            'default'      => '0',
            'system'       => false,
            'position'     => 999,
        ]);

        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'kluwer_exported');
        $attribute->setData('used_in_forms', ['adminhtml_customer'])->save();

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public static function getDependencies()
    {
        return [];
    }

    public function getAliases()
    {
        return [];
    }
}
