[2025-07-11T22:11:25.848825+00:00] main.ERROR: Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336 [] []
[2025-07-11T22:11:25.849742+00:00] main.CRITICAL: LogicException: Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336 in /home/<USER>/releases/202307041540/vendor/magento/framework/Acl/Builder.php:71
Stack trace:
#0 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/Acl/Builder/Proxy.php(95): Magento\Framework\Acl\Builder->getAcl()
#1 /home/<USER>/releases/202307041540/vendor/magento/module-backend/Model/Auth/Session.php(132): Magento\Framework\Acl\Builder\Proxy->getAcl()
#2 /home/<USER>/releases/202307041540/vendor/magento/module-backend/App/Action/Plugin/Authentication.php(144): Magento\Backend\Model\Auth\Session->refreshAcl()
#3 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(135): Magento\Backend\App\Action\Plugin\Authentication->aroundDispatch(Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor), Object(Closure), Object(Magento\Framework\App\Request\Http))
#4 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(153): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->Magento\Framework\Interception\{closure}(Object(Magento\Framework\App\Request\Http))
#5 /home/<USER>/releases/202307041540/generated/code/Magento/Backend/Controller/Adminhtml/Index/Index/Interceptor.php(32): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->___callPlugins('dispatch', Array, Array)
#6 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(245): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->dispatch(Object(Magento\Framework\App\Request\Http))
#7 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(212): Magento\Framework\App\FrontController->getActionResponse(Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor), Object(Magento\Framework\App\Request\Http))
#8 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(147): Magento\Framework\App\FrontController->processRequest(Object(Magento\Framework\App\Request\Http), Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor))
#9 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(58): Magento\Framework\App\FrontController->dispatch(Object(Magento\Framework\App\Request\Http))
#10 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(138): Magento\Framework\App\FrontController\Interceptor->___callParent('dispatch', Array)
#11 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(153): Magento\Framework\App\FrontController\Interceptor->Magento\Framework\Interception\{closure}(Object(Magento\Framework\App\Request\Http))
#12 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/App/FrontController/Interceptor.php(23): Magento\Framework\App\FrontController\Interceptor->___callPlugins('dispatch', Array, Array)
#13 /home/<USER>/releases/202307041540/vendor/magento/framework/App/Http.php(116): Magento\Framework\App\FrontController\Interceptor->dispatch(Object(Magento\Framework\App\Request\Http))
#14 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/App/Http/Interceptor.php(23): Magento\Framework\App\Http->launch()
#15 /home/<USER>/releases/202307041540/vendor/magento/framework/App/Bootstrap.php(264): Magento\Framework\App\Http\Interceptor->launch()
#16 /home/<USER>/releases/202307041540/pub/index.php(30): Magento\Framework\App\Bootstrap->run(Object(Magento\Framework\App\Http\Interceptor))
#17 {main} {"report_id":"75fc21e5113d2ac809533897fce3b35d6c6a375db5905e36d03d582b887f2c60","exception":"[object] (LogicException(code: 0): Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336 at /home/<USER>/releases/202307041540/vendor/magento/framework/Acl/Builder.php:71)"} []
[2025-07-11T22:11:32.535440+00:00] main.INFO: Cron Job catalog_product_outdated_price_values_cleanup is run [] []
[2025-07-11T22:11:32.652874+00:00] main.INFO: Cron Job catalog_product_outdated_price_values_cleanup is successfully finished. Statistics: {"sum":0.11784601211548,"count":1,"realmem":0,"emalloc":2135048,"realmem_start":140509184,"emalloc_start":79194384} [] []
[2025-07-11T22:11:32.658104+00:00] main.INFO: Cron Job catalog_product_frontend_actions_flush is run [] []
[2025-07-11T22:11:32.659682+00:00] main.INFO: Cron Job catalog_product_frontend_actions_flush is successfully finished. Statistics: {"sum":0.0015661716461182,"count":1,"realmem":0,"emalloc":1512,"realmem_start":140509184,"emalloc_start":81331544} [] []
[2025-07-11T22:11:32.664973+00:00] main.INFO: Cron Job outdated_authentication_failures_cleanup is run [] []
[2025-07-11T22:11:32.666306+00:00] main.INFO: Cron Job outdated_authentication_failures_cleanup is successfully finished. Statistics: {"sum":0.001317024230957,"count":1,"realmem":0,"emalloc":1448,"realmem_start":140509184,"emalloc_start":81334088} [] []
[2025-07-11T22:11:32.674772+00:00] main.INFO: Cron Job sales_grid_order_async_insert is run [] []
[2025-07-11T22:11:32.675934+00:00] main.INFO: Cron Job sales_grid_order_async_insert is successfully finished. Statistics: {"sum":0.0011398792266846,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":81407024} [] []
[2025-07-11T22:11:32.679662+00:00] main.INFO: Cron Job sales_grid_order_invoice_async_insert is run [] []
[2025-07-11T22:11:32.680452+00:00] main.INFO: Cron Job sales_grid_order_invoice_async_insert is successfully finished. Statistics: {"sum":0.00076794624328613,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":81411328} [] []
[2025-07-11T22:11:32.683566+00:00] main.INFO: Cron Job sales_grid_order_shipment_async_insert is run [] []
[2025-07-11T22:11:32.684165+00:00] main.INFO: Cron Job sales_grid_order_shipment_async_insert is successfully finished. Statistics: {"sum":0.00058889389038086,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":81414784} [] []
[2025-07-11T22:11:32.687187+00:00] main.INFO: Cron Job sales_grid_order_creditmemo_async_insert is run [] []
[2025-07-11T22:11:32.687783+00:00] main.INFO: Cron Job sales_grid_order_creditmemo_async_insert is successfully finished. Statistics: {"sum":0.00057601928710938,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":81418368} [] []
[2025-07-11T22:11:32.712328+00:00] main.INFO: Cron Job sales_send_order_emails is run [] []
[2025-07-11T22:11:32.713086+00:00] main.INFO: Cron Job sales_send_order_emails is successfully finished. Statistics: {"sum":0.00075387954711914,"count":1,"realmem":0,"emalloc":960,"realmem_start":140509184,"emalloc_start":82716848} [] []
[2025-07-11T22:11:32.722946+00:00] main.INFO: Cron Job sales_send_order_invoice_emails is run [] []
[2025-07-11T22:11:32.723687+00:00] main.INFO: Cron Job sales_send_order_invoice_emails is successfully finished. Statistics: {"sum":0.00072216987609863,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":82853112} [] []
[2025-07-11T22:11:32.736102+00:00] main.INFO: Cron Job sales_send_order_shipment_emails is run [] []
[2025-07-11T22:11:32.737296+00:00] main.INFO: Cron Job sales_send_order_shipment_emails is successfully finished. Statistics: {"sum":0.0011849403381348,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":82991528} [] []
[2025-07-11T22:11:32.750164+00:00] main.INFO: Cron Job sales_send_order_creditmemo_emails is run [] []
[2025-07-11T22:11:32.751329+00:00] main.INFO: Cron Job sales_send_order_creditmemo_emails is successfully finished. Statistics: {"sum":0.0011539459228516,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":83193680} [] []
[2025-07-11T22:11:32.758187+00:00] main.INFO: Cron Job inventory_in_store_pickup_sales_send_order_notified_emails is run [] []
[2025-07-11T22:11:32.759286+00:00] main.INFO: Cron Job inventory_in_store_pickup_sales_send_order_notified_emails is successfully finished. Statistics: {"sum":0.0010838508605957,"count":1,"realmem":0,"emalloc":1072,"realmem_start":140509184,"emalloc_start":83193784} [] []
[2025-07-11T22:11:32.764081+00:00] main.INFO: Cron Job bulk_cleanup is run [] []
[2025-07-11T22:11:32.765424+00:00] main.INFO: Cron Job bulk_cleanup is successfully finished. Statistics: {"sum":0.0013229846954346,"count":1,"realmem":0,"emalloc":1608,"realmem_start":140509184,"emalloc_start":83194400} [] []
[2025-07-11T22:11:32.769615+00:00] main.INFO: Cron Job amasty_cron_activity is run [] []
[2025-07-11T22:11:32.783195+00:00] main.INFO: Cron Job amasty_cron_activity is successfully finished. Statistics: {"sum":0.013503074645996,"count":1,"realmem":0,"emalloc":161696,"realmem_start":140509184,"emalloc_start":83195864} [] []
[2025-07-11T22:11:32.805063+00:00] main.INFO: Cron Job mageworx_order_editor_webhooks_send is run [] []
[2025-07-11T22:11:32.809354+00:00] main.INFO: Cron Job mageworx_order_editor_webhooks_send is successfully finished. Statistics: {"sum":0.0042641162872314,"count":1,"realmem":0,"emalloc":712872,"realmem_start":140509184,"emalloc_start":83499576} [] []
[2025-07-11T22:11:32.814700+00:00] main.INFO: Cron Job mst_report_email is run [] []
[2025-07-11T22:11:32.817814+00:00] main.INFO: Cron Job mst_report_email is successfully finished. Statistics: {"sum":0.0030930042266846,"count":1,"realmem":0,"emalloc":67264,"realmem_start":140509184,"emalloc_start":84212984} [] []
[2025-07-11T22:11:34.029631+00:00] main.INFO: Cron Job indexer_reindex_all_invalid is run [] []
[2025-07-11T22:11:34.030280+00:00] main.INFO: Cron Job magefan_blog_cron_resave_existing_posts is run [] []
[2025-07-11T22:11:34.031354+00:00] main.INFO: Cron Job magefan_blog_cron_resave_existing_posts is successfully finished. Statistics: {"sum":0.0016369819641113,"count":1,"realmem":0,"emalloc":2712,"realmem_start":140509184,"emalloc_start":78649664} [] []
[2025-07-11T22:11:34.053438+00:00] main.INFO: Cron Job indexer_reindex_all_invalid is successfully finished. Statistics: {"sum":0.024334907531738,"count":1,"realmem":0,"emalloc":211904,"realmem_start":138412032,"emalloc_start":78629544} [] []
[2025-07-11T22:11:34.057826+00:00] main.INFO: Cron Job indexer_update_all_views is run [] []
[2025-07-11T22:11:34.203889+00:00] main.INFO: Cron Job indexer_update_all_views is successfully finished. Statistics: {"sum":0.14602303504944,"count":1,"realmem":0,"emalloc":1643088,"realmem_start":138412032,"emalloc_start":78841832} [] []
[2025-07-11T22:11:34.283954+00:00] main.INFO: Cron Job consumers_runner is run [] []
[2025-07-11T22:11:34.304268+00:00] main.INFO: Cron Job consumers_runner is successfully finished. Statistics: {"sum":0.020696878433228,"count":1,"realmem":0,"emalloc":78264,"realmem_start":138412032,"emalloc_start":78770320} [] []
[2025-07-11T22:11:35.910138+00:00] main.DEBUG: cache_invalidate:  {"method":"GET","url":"https://mistercarpets.com/fr/","invalidateInfo":{"servers":[{"Laminas\\Uri\\Uri":"http://86.109.16.203:8080/"}],"formattedTagsChunk":"((^|,)nwdthemes_revslider_option_29(,|$))"}} []
[2025-07-11T22:11:35.955024+00:00] main.DEBUG: cache_invalidate:  {"method":"GET","url":"https://mistercarpets.com/fr/","invalidateInfo":{"servers":[{"Laminas\\Uri\\Uri":"http://86.109.16.203:8080/"}],"formattedTagsChunk":"((^|,)nwdthemes_revslider_option_29(,|$))"}} []
