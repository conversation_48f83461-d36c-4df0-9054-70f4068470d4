<?php
namespace Mageya<PERSON>lim\KluwerExport\Block\Adminhtml\Export;

use Magento\Backend\Block\Widget\Grid\Extended;

class Grid extends Extended
{
    protected function _prepareCollection()
    {
        $collection = \Magento\Framework\App\ObjectManager::getInstance()
            ->create(\Mageyazilim\KluwerExport\Model\ResourceModel\Export\Collection::class);
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    protected function _prepareColumns()
    {
        $this->addColumn('period', ['header' => __('Period'), 'index' => 'period']);
        $this->addColumn('created_at', ['header' => __('Created At'), 'index' => 'created_at']);
        return parent::_prepareColumns();
    }
}
