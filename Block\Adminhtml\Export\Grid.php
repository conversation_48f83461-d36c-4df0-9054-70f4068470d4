<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Block\Adminhtml\Export;

use Magento\Backend\Block\Widget\Grid\Extended;
use Magento\Backend\Block\Template\Context;
use Magento\Backend\Helper\Data;
use Mageya<PERSON>lim\KluwerExport\Model\ResourceModel\Export\CollectionFactory;

class Grid extends Extended
{
    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @param Context $context
     * @param Data $backendHelper
     * @param CollectionFactory $collectionFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $backendHelper,
        CollectionFactory $collectionFactory,
        array $data = []
    ) {
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context, $backendHelper, $data);
    }

    protected function _prepareCollection()
    {
        $collection = $this->collectionFactory->create();
        $this->setCollection($collection);
        return parent::_prepareCollection();
    }

    protected function _prepareColumns()
    {
        $this->addColumn('export_id', [
            'header' => __('ID'),
            'index' => 'export_id',
            'type' => 'number'
        ]);

        $this->addColumn('period', [
            'header' => __('Period'),
            'index' => 'period'
        ]);

        $this->addColumn('filename', [
            'header' => __('Files'),
            'index' => 'filename'
        ]);

        $this->addColumn('orders_count', [
            'header' => __('Orders Count'),
            'index' => 'orders_count',
            'type' => 'number'
        ]);

        $this->addColumn('created_at', [
            'header' => __('Created At'),
            'index' => 'created_at',
            'type' => 'datetime'
        ]);

        return parent::_prepareColumns();
    }
}
