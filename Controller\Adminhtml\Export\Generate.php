<?php
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\KluwerExport\Controller\Adminhtml\Export;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Mageyazilim\KluwerExport\Model\Export;

class Generate extends Action
{
    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * @var Export
     */
    protected $exportModel;

    /**
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param Export $exportModel
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        Export $exportModel
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->exportModel = $exportModel;
    }

    /**
     * Generate export
     *
     * @return \Magento\Framework\Controller\Result\Json
     */
    public function execute()
    {
        $result = $this->resultJsonFactory->create();
        
        try {
            $fromDate = $this->getRequest()->getParam('from_date');
            $toDate = $this->getRequest()->getParam('to_date');
            $period = $this->getRequest()->getParam('period', 'monthly');

            $exportResult = $this->exportModel->generateExport($fromDate, $toDate, $period);

            if ($exportResult['success']) {
                $this->messageManager->addSuccessMessage(
                    __('Export generated successfully. Period: %1', $exportResult['period_folder'])
                );
                return $result->setData([
                    'success' => true,
                    'message' => __('Export generated successfully'),
                    'sales_filename' => $exportResult['sales_filename'],
                    'customers_filename' => $exportResult['customers_filename'],
                    'period_folder' => $exportResult['period_folder']
                ]);
            } else {
                $this->messageManager->addErrorMessage($exportResult['message']);
                return $result->setData([
                    'success' => false,
                    'message' => $exportResult['message']
                ]);
            }
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Error generating export: %1', $e->getMessage()));
            return $result->setData([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Check if admin has permissions to visit related pages
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Mageyazilim_KluwerExport::export_generate');
    }
}
