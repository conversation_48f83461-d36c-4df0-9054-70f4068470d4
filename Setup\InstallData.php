<?php

namespace Ma<PERSON><PERSON><PERSON><PERSON>\KluwerExport\Setup;

use Magento\Customer\Model\Customer;
use <PERSON>gento\Customer\Setup\CustomerSetupFactory;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class InstallData implements InstallDataInterface
{
    /**
     * @var CustomerSetupFactory
     */
    protected $customerSetupFactory;

    /**
     * @param CustomerSetupFactory $customerSetupFactory
     */
    public function __construct(CustomerSetupFactory $customerSetupFactory)
    {
        $this->customerSetupFactory = $customerSetupFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);

        // Add kluwer_exported attribute
        $customerSetup->addAttribute(Customer::ENTITY, 'kluwer_exported', [
            'type' => 'int',
            'label' => 'Kluwer Exported',
            'input' => 'boolean',
            'required' => false,
            'default' => 0,
            'system' => false,
            'user_defined' => true,
            'is_used_in_grid' => true,
            'is_visible_in_grid' => true,
            'is_filterable_in_grid' => true,
            'is_searchable_in_grid' => true,
            'visible' => true,
            'position' => 200
        ]);

        // Add attribute to forms
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'kluwer_exported');
        $attribute->addData([
            'used_in_forms' => ['adminhtml_customer']
        ]);
        $attribute->save();

        $setup->endSetup();
    }
}
