[2025-07-11T22:11:25.849742+00:00] main.CRITICAL: LogicException: Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336 in /home/<USER>/releases/202307041540/vendor/magento/framework/Acl/Builder.php:71
Stack trace:
#0 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/Acl/Builder/Proxy.php(95): Magento\Framework\Acl\Builder->getAcl()
#1 /home/<USER>/releases/202307041540/vendor/magento/module-backend/Model/Auth/Session.php(132): Magento\Framework\Acl\Builder\Proxy->getAcl()
#2 /home/<USER>/releases/202307041540/vendor/magento/module-backend/App/Action/Plugin/Authentication.php(144): Magento\Backend\Model\Auth\Session->refreshAcl()
#3 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(135): Magento\Backend\App\Action\Plugin\Authentication->aroundDispatch(Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor), Object(Closure), Object(Magento\Framework\App\Request\Http))
#4 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(153): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->Magento\Framework\Interception\{closure}(Object(Magento\Framework\App\Request\Http))
#5 /home/<USER>/releases/202307041540/generated/code/Magento/Backend/Controller/Adminhtml/Index/Index/Interceptor.php(32): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->___callPlugins('dispatch', Array, Array)
#6 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(245): Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor->dispatch(Object(Magento\Framework\App\Request\Http))
#7 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(212): Magento\Framework\App\FrontController->getActionResponse(Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor), Object(Magento\Framework\App\Request\Http))
#8 /home/<USER>/releases/202307041540/vendor/magento/framework/App/FrontController.php(147): Magento\Framework\App\FrontController->processRequest(Object(Magento\Framework\App\Request\Http), Object(Magento\Backend\Controller\Adminhtml\Index\Index\Interceptor))
#9 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(58): Magento\Framework\App\FrontController->dispatch(Object(Magento\Framework\App\Request\Http))
#10 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(138): Magento\Framework\App\FrontController\Interceptor->___callParent('dispatch', Array)
#11 /home/<USER>/releases/202307041540/vendor/magento/framework/Interception/Interceptor.php(153): Magento\Framework\App\FrontController\Interceptor->Magento\Framework\Interception\{closure}(Object(Magento\Framework\App\Request\Http))
#12 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/App/FrontController/Interceptor.php(23): Magento\Framework\App\FrontController\Interceptor->___callPlugins('dispatch', Array, Array)
#13 /home/<USER>/releases/202307041540/vendor/magento/framework/App/Http.php(116): Magento\Framework\App\FrontController\Interceptor->dispatch(Object(Magento\Framework\App\Request\Http))
#14 /home/<USER>/releases/202307041540/generated/code/Magento/Framework/App/Http/Interceptor.php(23): Magento\Framework\App\Http->launch()
#15 /home/<USER>/releases/202307041540/vendor/magento/framework/App/Bootstrap.php(264): Magento\Framework\App\Http\Interceptor->launch()
#16 /home/<USER>/releases/202307041540/pub/index.php(30): Magento\Framework\App\Bootstrap->run(Object(Magento\Framework\App\Http\Interceptor))
#17 {main} {"report_id":"75fc21e5113d2ac809533897fce3b35d6c6a375db5905e36d03d582b887f2c60","exception":"[object] (LogicException(code: 0): Could not create an acl object: Warning: DOMXPath::query(): Invalid expression in /home/<USER>/releases/202307041540/vendor/magento/framework/Config/Dom.php on line 336 at /home/<USER>/releases/202307041540/vendor/magento/framework/Acl/Builder.php:71)"} []
